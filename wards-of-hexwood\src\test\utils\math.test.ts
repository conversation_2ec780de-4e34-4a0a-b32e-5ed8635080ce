/**
 * Tests for mathematical utility functions
 */

import { describe, it, expect, vi } from 'vitest';
import { rand, clamp, rollDie, actionSucceeds, pickWeighted, tempShift, formatCountdown } from '../../utils/math';

describe('Math Utils', () => {
  describe('rand', () => {
    it('should return a number within the specified range', () => {
      for (let i = 0; i < 100; i++) {
        const result = rand(1, 6);
        expect(result).toBeGreaterThanOrEqual(1);
        expect(result).toBeLessThanOrEqual(6);
        expect(Number.isInteger(result)).toBe(true);
      }
    });

    it('should handle single value range', () => {
      expect(rand(5, 5)).toBe(5);
    });
  });

  describe('clamp', () => {
    it('should clamp values within bounds', () => {
      expect(clamp(5, 0, 10)).toBe(5);
      expect(clamp(-1, 0, 10)).toBe(0);
      expect(clamp(11, 0, 10)).toBe(10);
      expect(clamp(0, 0, 10)).toBe(0);
      expect(clamp(10, 0, 10)).toBe(10);
    });
  });

  describe('rollDie', () => {
    it('should return values between 1 and 6', () => {
      for (let i = 0; i < 100; i++) {
        const result = rollDie();
        expect(result).toBeGreaterThanOrEqual(1);
        expect(result).toBeLessThanOrEqual(6);
        expect(Number.isInteger(result)).toBe(true);
      }
    });
  });

  describe('actionSucceeds', () => {
    it('should have higher success rate with higher die values', () => {
      // Mock Math.random to return a consistent value
      const mockRandom = vi.spyOn(Math, 'random').mockReturnValue(0.5);
      
      const lowDieSuccess = actionSucceeds(35, 1, 10); // 35 + 10 - 10 = 35% chance
      const highDieSuccess = actionSucceeds(35, 6, 10); // 35 + 60 - 10 = 85% chance
      
      // With 0.5 random (50%), low die should fail, high die should succeed
      expect(lowDieSuccess).toBe(false);
      expect(highDieSuccess).toBe(true);
      
      mockRandom.mockRestore();
    });

    it('should respect minimum and maximum bounds', () => {
      const mockRandom = vi.spyOn(Math, 'random').mockReturnValue(0.01); // 1%
      
      // Should succeed even with terrible stats due to 5% minimum
      expect(actionSucceeds(0, 1, 50)).toBe(true);
      
      mockRandom.mockReturnValue(0.99); // 99%
      
      // Should fail even with great stats due to 95% maximum
      expect(actionSucceeds(100, 6, 0)).toBe(false);
      
      mockRandom.mockRestore();
    });
  });

  describe('pickWeighted', () => {
    it('should pick items based on weight', () => {
      const items = [
        { name: 'common', weight: 10 },
        { name: 'rare', weight: 1 }
      ];
      
      const results = { common: 0, rare: 0 };
      
      // Run many times to test distribution
      for (let i = 0; i < 1000; i++) {
        const picked = pickWeighted(items);
        results[picked.name as keyof typeof results]++;
      }
      
      // Common should be picked much more often than rare
      expect(results.common).toBeGreaterThan(results.rare * 5);
    });
  });

  describe('tempShift', () => {
    it('should move temperature toward ambient', () => {
      const result = tempShift(50, 10, 0, 0);
      expect(result).toBeLessThan(50);
      expect(result).toBeGreaterThan(10);
    });

    it('should apply heat and cold buffs', () => {
      const baseline = tempShift(50, 50, 0, 0);
      const withHeat = tempShift(50, 50, 10, 0);
      const withCold = tempShift(50, 50, 0, 10);
      
      expect(withHeat).toBeGreaterThan(baseline);
      expect(withCold).toBeLessThan(baseline);
    });

    it('should respect temperature bounds', () => {
      expect(tempShift(0, 0, 0, 100)).toBe(0);
      expect(tempShift(100, 100, 100, 0)).toBe(100);
    });
  });

  describe('formatCountdown', () => {
    it('should format time correctly', () => {
      expect(formatCountdown(0)).toBe('out');
      expect(formatCountdown(-1000)).toBe('out');
      expect(formatCountdown(30000)).toBe('30s');
      expect(formatCountdown(90000)).toBe('1m 30s');
      expect(formatCountdown(3661000)).toBe('1h 1m');
    });
  });
});
