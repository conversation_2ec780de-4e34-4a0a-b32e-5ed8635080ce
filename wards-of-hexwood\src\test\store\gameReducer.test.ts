/**
 * Tests for game state reducer
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { gameReducer } from '../../store/gameReducer';
import { createInitialState } from '../../utils/persistence';
import type { GameState } from '../../types/game';

describe('Game Reducer', () => {
  let initialState: GameState;

  beforeEach(() => {
    initialState = createInitialState();
  });

  describe('ROLL_DICE', () => {
    it('should create dice equal to maxDice', () => {
      const result = gameReducer(initialState, { type: 'ROLL_DICE' });
      
      expect(result.dice).toHaveLength(initialState.flags.maxDice);
      result.dice.forEach(die => {
        expect(die.value).toBeGreaterThanOrEqual(1);
        expect(die.value).toBeLessThanOrEqual(6);
        expect(die.assignedTo).toBeNull();
        expect(die.id).toMatch(/^d\d+-\d+$/);
      });
    });

    it('should not roll dice if already rolled', () => {
      const stateWithDice = { ...initialState, dice: [{ id: 'test', value: 3, assignedTo: null }] };
      const result = gameReducer(stateWithDice, { type: 'ROLL_DICE' });
      
      expect(result.dice).toEqual(stateWithDice.dice);
    });
  });

  describe('SELECT_DIE', () => {
    it('should set selectedDieId', () => {
      const result = gameReducer(initialState, { type: 'SELECT_DIE', id: 'test-die' });
      
      expect(result.selectedDieId).toBe('test-die');
    });

    it('should clear selectedDieId when passed null', () => {
      const stateWithSelection = { ...initialState, selectedDieId: 'test-die' };
      const result = gameReducer(stateWithSelection, { type: 'SELECT_DIE', id: null });
      
      expect(result.selectedDieId).toBeNull();
    });
  });

  describe('ASSIGN_TO_ACTION', () => {
    it('should assign selected die to action', () => {
      const stateWithDice = {
        ...initialState,
        dice: [{ id: 'test-die', value: 4, assignedTo: null }],
        selectedDieId: 'test-die'
      };

      const result = gameReducer(stateWithDice, { 
        type: 'ASSIGN_TO_ACTION', 
        action: 'forageFood' 
      });

      expect(result.assignments.forageFood).toBe('test-die');
      expect(result.dice[0].assignedTo).toBe('forageFood');
      expect(result.selectedDieId).toBeNull();
    });

    it('should not assign if no die is selected', () => {
      const result = gameReducer(initialState, { 
        type: 'ASSIGN_TO_ACTION', 
        action: 'forageFood' 
      });

      expect(result.assignments.forageFood).toBeNull();
      expect(result).toEqual(initialState);
    });

    it('should prevent double assignment', () => {
      const stateWithAssignment = {
        ...initialState,
        dice: [{ id: 'test-die', value: 4, assignedTo: 'gatherWater' }],
        selectedDieId: 'test-die',
        assignments: { ...initialState.assignments, gatherWater: 'test-die' }
      };

      const result = gameReducer(stateWithAssignment, { 
        type: 'ASSIGN_TO_ACTION', 
        action: 'forageFood' 
      });

      expect(result.assignments.forageFood).toBeNull();
      expect(result.assignments.gatherWater).toBe('test-die');
    });
  });

  describe('RESET', () => {
    it('should return to initial state', () => {
      const modifiedState = {
        ...initialState,
        stats: { ...initialState.stats, health: 50 },
        flags: { ...initialState.flags, day: 5 }
      };

      const result = gameReducer(modifiedState, { type: 'RESET' });

      expect(result.stats.health).toBe(initialState.stats.health);
      expect(result.flags.day).toBe(initialState.flags.day);
    });
  });

  describe('RESOLVE_DAY', () => {
    it('should require all dice to be assigned', () => {
      const stateWithUnassignedDice = {
        ...initialState,
        dice: [{ id: 'test-die', value: 4, assignedTo: null }],
        flags: { ...initialState.flags, maxDice: 1 }
      };

      const result = gameReducer(stateWithUnassignedDice, { type: 'RESOLVE_DAY' });

      expect(result.chronicle[0].text).toContain('must assign all dice');
    });

    it('should advance day when all dice are assigned', () => {
      const stateReadyForDay = {
        ...initialState,
        dice: [{ id: 'test-die', value: 4, assignedTo: 'forageFood' }],
        assignments: { ...initialState.assignments, forageFood: 'test-die' },
        flags: { ...initialState.flags, maxDice: 1 }
      };

      // Mock localStorage to avoid errors
      const mockSetItem = vi.spyOn(Storage.prototype, 'setItem').mockImplementation(() => {});

      const result = gameReducer(stateReadyForDay, { type: 'RESOLVE_DAY' });

      expect(result.flags.day).toBe(initialState.flags.day + 1);
      expect(result.dice).toHaveLength(0);
      expect(Object.values(result.assignments).every(v => v === null)).toBe(true);

      mockSetItem.mockRestore();
    });
  });

  describe('LOAD', () => {
    it('should load and normalize provided state', () => {
      const partialState = {
        stats: { health: 50 },
        flags: { day: 3 }
      };

      const result = gameReducer(initialState, { 
        type: 'LOAD', 
        state: partialState as GameState 
      });

      expect(result.stats.health).toBe(50);
      expect(result.flags.day).toBe(3);
      // Should have normalized missing properties
      expect(result.resources).toBeDefined();
      expect(result.inventory).toBeDefined();
    });
  });
});
