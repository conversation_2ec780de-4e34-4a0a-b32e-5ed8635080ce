{"version": 3, "file": "resolve.js", "sources": ["../../../src/js/resolve.ts"], "sourcesContent": ["/**\n * resolve\n */\n\nimport {\n  CacheItem,\n  NullObject,\n  createCacheKey,\n  getCache,\n  setCache\n} from './cache';\nimport {\n  convertRgbToHex,\n  resolveColorFunc,\n  resolveColorMix,\n  resolveColorValue\n} from './color';\nimport { isString } from './common';\nimport { cssCalc } from './css-calc';\nimport { resolveVar } from './css-var';\nimport { resolveRelativeColor } from './relative-color';\nimport { splitValue } from './util';\nimport {\n  ComputedColorChannels,\n  Options,\n  SpecifiedColorChannels\n} from './typedef';\n\n/* constants */\nimport {\n  FN_COLOR,\n  FN_MIX,\n  SYN_FN_CALC,\n  SYN_FN_LIGHT_DARK,\n  SYN_FN_REL,\n  SYN_FN_VAR,\n  VAL_COMP,\n  VAL_SPEC\n} from './constant';\nconst NAMESPACE = 'resolve';\nconst RGB_TRANSPARENT = 'rgba(0, 0, 0, 0)';\n\n/* regexp */\nconst REG_FN_CALC = new RegExp(SYN_FN_CALC);\nconst REG_FN_LIGHT_DARK = new RegExp(SYN_FN_LIGHT_DARK);\nconst REG_FN_REL = new RegExp(SYN_FN_REL);\nconst REG_FN_VAR = new RegExp(SYN_FN_VAR);\n\n/**\n * resolve color\n * @param value - CSS color value\n * @param [opt] - options\n * @returns resolved color\n */\nexport const resolveColor = (\n  value: string,\n  opt: Options = {}\n): string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const {\n    colorScheme = 'normal',\n    currentColor = '',\n    format = VAL_COMP,\n    nullable = false\n  } = opt;\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'resolve',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    return cachedResult.item as string;\n  }\n  if (REG_FN_VAR.test(value)) {\n    if (format === VAL_SPEC) {\n      setCache(cacheKey, value);\n      return value;\n    }\n    const resolvedValue = resolveVar(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      switch (format) {\n        case 'hex':\n        case 'hexAlpha': {\n          setCache(cacheKey, resolvedValue);\n          return resolvedValue;\n        }\n        default: {\n          if (nullable) {\n            setCache(cacheKey, resolvedValue);\n            return resolvedValue;\n          }\n          const res = RGB_TRANSPARENT;\n          setCache(cacheKey, res);\n          return res;\n        }\n      }\n    } else {\n      value = resolvedValue;\n    }\n  }\n  if (opt.format !== format) {\n    opt.format = format;\n  }\n  value = value.toLowerCase();\n  if (REG_FN_LIGHT_DARK.test(value) && value.endsWith(')')) {\n    const colorParts = value.replace(REG_FN_LIGHT_DARK, '').replace(/\\)$/, '');\n    const [light = '', dark = ''] = splitValue(colorParts, {\n      delimiter: ','\n    });\n    if (light && dark) {\n      if (format === VAL_SPEC) {\n        const lightColor = resolveColor(light, opt);\n        const darkColor = resolveColor(dark, opt);\n        let res;\n        if (lightColor && darkColor) {\n          res = `light-dark(${lightColor}, ${darkColor})`;\n        } else {\n          res = '';\n        }\n        setCache(cacheKey, res);\n        return res;\n      }\n      let resolvedValue;\n      if (colorScheme === 'dark') {\n        resolvedValue = resolveColor(dark, opt);\n      } else {\n        resolvedValue = resolveColor(light, opt);\n      }\n      let res;\n      if (resolvedValue instanceof NullObject) {\n        if (nullable) {\n          res = resolvedValue;\n        } else {\n          res = RGB_TRANSPARENT;\n        }\n      } else {\n        res = resolvedValue;\n      }\n      setCache(cacheKey, res);\n      return res;\n    }\n    // invalid value\n    switch (format) {\n      case VAL_SPEC: {\n        setCache(cacheKey, '');\n        return '';\n      }\n      case 'hex':\n      case 'hexAlpha': {\n        setCache(cacheKey, null);\n        return new NullObject();\n      }\n      case VAL_COMP:\n      default: {\n        const res = RGB_TRANSPARENT;\n        setCache(cacheKey, res);\n        return res;\n      }\n    }\n  }\n  if (REG_FN_REL.test(value)) {\n    const resolvedValue = resolveRelativeColor(value, opt);\n    if (format === VAL_COMP) {\n      let res;\n      if (resolvedValue instanceof NullObject) {\n        if (nullable) {\n          res = resolvedValue;\n        } else {\n          res = RGB_TRANSPARENT;\n        }\n      } else {\n        res = resolvedValue;\n      }\n      setCache(cacheKey, res);\n      return res;\n    }\n    if (format === VAL_SPEC) {\n      let res = '';\n      if (resolvedValue instanceof NullObject) {\n        res = '';\n      } else {\n        res = resolvedValue;\n      }\n      setCache(cacheKey, res);\n      return res;\n    }\n    if (resolvedValue instanceof NullObject) {\n      value = '';\n    } else {\n      value = resolvedValue;\n    }\n  }\n  if (REG_FN_CALC.test(value)) {\n    value = cssCalc(value, opt);\n  }\n  let cs = '';\n  let r = NaN;\n  let g = NaN;\n  let b = NaN;\n  let alpha = NaN;\n  if (value === 'transparent') {\n    switch (format) {\n      case VAL_SPEC: {\n        setCache(cacheKey, value);\n        return value;\n      }\n      case 'hex': {\n        setCache(cacheKey, null);\n        return new NullObject();\n      }\n      case 'hexAlpha': {\n        const res = '#00000000';\n        setCache(cacheKey, res);\n        return res;\n      }\n      case VAL_COMP:\n      default: {\n        const res = RGB_TRANSPARENT;\n        setCache(cacheKey, res);\n        return res;\n      }\n    }\n  } else if (value === 'currentcolor') {\n    if (format === VAL_SPEC) {\n      setCache(cacheKey, value);\n      return value;\n    }\n    if (currentColor) {\n      let resolvedValue;\n      if (currentColor.startsWith(FN_MIX)) {\n        resolvedValue = resolveColorMix(currentColor, opt);\n      } else if (currentColor.startsWith(FN_COLOR)) {\n        resolvedValue = resolveColorFunc(currentColor, opt);\n      } else {\n        resolvedValue = resolveColorValue(currentColor, opt);\n      }\n      if (resolvedValue instanceof NullObject) {\n        setCache(cacheKey, resolvedValue);\n        return resolvedValue;\n      }\n      [cs, r, g, b, alpha] = resolvedValue as ComputedColorChannels;\n    } else if (format === VAL_COMP) {\n      const res = RGB_TRANSPARENT;\n      setCache(cacheKey, res);\n      return res;\n    }\n  } else if (format === VAL_SPEC) {\n    if (value.startsWith(FN_MIX)) {\n      const res = resolveColorMix(value, opt) as string;\n      setCache(cacheKey, res);\n      return res;\n    } else if (value.startsWith(FN_COLOR)) {\n      const [scs, rr, gg, bb, aa] = resolveColorFunc(\n        value,\n        opt\n      ) as SpecifiedColorChannels;\n      let res = '';\n      if (aa === 1) {\n        res = `color(${scs} ${rr} ${gg} ${bb})`;\n      } else {\n        res = `color(${scs} ${rr} ${gg} ${bb} / ${aa})`;\n      }\n      setCache(cacheKey, res);\n      return res;\n    } else {\n      const rgb = resolveColorValue(value, opt);\n      if (isString(rgb)) {\n        setCache(cacheKey, rgb);\n        return rgb;\n      }\n      const [scs, rr, gg, bb, aa] = rgb as SpecifiedColorChannels;\n      let res = '';\n      if (scs === 'rgb') {\n        if (aa === 1) {\n          res = `${scs}(${rr}, ${gg}, ${bb})`;\n        } else {\n          res = `${scs}a(${rr}, ${gg}, ${bb}, ${aa})`;\n        }\n      } else if (aa === 1) {\n        res = `${scs}(${rr} ${gg} ${bb})`;\n      } else {\n        res = `${scs}(${rr} ${gg} ${bb} / ${aa})`;\n      }\n      setCache(cacheKey, res);\n      return res;\n    }\n  } else if (value.startsWith(FN_MIX)) {\n    if (/currentcolor/.test(value)) {\n      if (currentColor) {\n        value = value.replace(/currentcolor/g, currentColor);\n      }\n    }\n    if (/transparent/.test(value)) {\n      value = value.replace(/transparent/g, RGB_TRANSPARENT);\n    }\n    const resolvedValue = resolveColorMix(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      setCache(cacheKey, resolvedValue);\n      return resolvedValue;\n    }\n    [cs, r, g, b, alpha] = resolvedValue as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    const resolvedValue = resolveColorFunc(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      setCache(cacheKey, resolvedValue);\n      return resolvedValue;\n    }\n    [cs, r, g, b, alpha] = resolvedValue as ComputedColorChannels;\n  } else if (value) {\n    const resolvedValue = resolveColorValue(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      setCache(cacheKey, resolvedValue);\n      return resolvedValue;\n    }\n    [cs, r, g, b, alpha] = resolvedValue as ComputedColorChannels;\n  }\n  let res = '';\n  switch (format) {\n    case 'hex': {\n      if (\n        Number.isNaN(r) ||\n        Number.isNaN(g) ||\n        Number.isNaN(b) ||\n        Number.isNaN(alpha) ||\n        alpha === 0\n      ) {\n        setCache(cacheKey, null);\n        return new NullObject();\n      }\n      res = convertRgbToHex([r, g, b, 1]);\n      break;\n    }\n    case 'hexAlpha': {\n      if (\n        Number.isNaN(r) ||\n        Number.isNaN(g) ||\n        Number.isNaN(b) ||\n        Number.isNaN(alpha)\n      ) {\n        setCache(cacheKey, null);\n        return new NullObject();\n      }\n      res = convertRgbToHex([r, g, b, alpha]);\n      break;\n    }\n    case VAL_COMP:\n    default: {\n      switch (cs) {\n        case 'rgb': {\n          if (alpha === 1) {\n            res = `${cs}(${r}, ${g}, ${b})`;\n          } else {\n            res = `${cs}a(${r}, ${g}, ${b}, ${alpha})`;\n          }\n          break;\n        }\n        case 'lab':\n        case 'lch':\n        case 'oklab':\n        case 'oklch': {\n          if (alpha === 1) {\n            res = `${cs}(${r} ${g} ${b})`;\n          } else {\n            res = `${cs}(${r} ${g} ${b} / ${alpha})`;\n          }\n          break;\n        }\n        // color()\n        default: {\n          if (alpha === 1) {\n            res = `color(${cs} ${r} ${g} ${b})`;\n          } else {\n            res = `color(${cs} ${r} ${g} ${b} / ${alpha})`;\n          }\n        }\n      }\n    }\n  }\n  setCache(cacheKey, res);\n  return res;\n};\n\n/**\n * resolve CSS color\n * @param value\n *   - CSS color value\n *   - system colors are not supported\n * @param [opt] - options\n * @param [opt.currentColor]\n *   - color to use for `currentcolor` keyword\n *   - if omitted, it will be treated as a missing color\n *     i.e. `rgb(none none none / none)`\n * @param [opt.customProperty]\n *   - custom properties\n *   - pair of `--` prefixed property name and value,\n *     e.g. `customProperty: { '--some-color': '#0000ff' }`\n *   - and/or `callback` function to get the value of the custom property,\n *     e.g. `customProperty: { callback: someDeclaration.getPropertyValue }`\n * @param [opt.dimension]\n *   - dimension, convert relative length to pixels\n *   - pair of unit and it's value as a number in pixels,\n *     e.g. `dimension: { em: 12, rem: 16, vw: 10.26 }`\n *   - and/or `callback` function to get the value as a number in pixels,\n *     e.g. `dimension: { callback: convertUnitToPixel }`\n * @param [opt.format]\n *   - output format, one of below\n *   - `computedValue` (default), [computed value][139] of the color\n *   - `specifiedValue`, [specified value][140] of the color\n *   - `hex`, hex color notation, i.e. `rrggbb`\n *   - `hexAlpha`, hex color notation with alpha channel, i.e. `#rrggbbaa`\n * @returns\n *   - one of rgba?(), #rrggbb(aa)?, color-name, '(empty-string)',\n *     color(color-space r g b / alpha), color(color-space x y z / alpha),\n *     lab(l a b / alpha), lch(l c h / alpha), oklab(l a b / alpha),\n *     oklch(l c h / alpha), null\n *   - in `computedValue`, values are numbers, however `rgb()` values are\n *     integers\n *   - in `specifiedValue`, returns `empty string` for unknown and/or invalid\n *     color\n *   - in `hex`, returns `null` for `transparent`, and also returns `null` if\n *     any of `r`, `g`, `b`, `alpha` is not a number\n *   - in `hexAlpha`, returns `#00000000` for `transparent`,\n *     however returns `null` if any of `r`, `g`, `b`, `alpha` is not a number\n */\nexport const resolve = (value: string, opt: Options = {}): string | null => {\n  opt.nullable = false;\n  const resolvedValue = resolveColor(value, opt);\n  if (resolvedValue instanceof NullObject) {\n    return null;\n  }\n  return resolvedValue as string;\n};\n"], "names": ["res"], "mappings": ";;;;;;;;AAuCA,MAAM,YAAY;AAClB,MAAM,kBAAkB;AAGxB,MAAM,cAAc,IAAI,OAAO,WAAW;AAC1C,MAAM,oBAAoB,IAAI,OAAO,iBAAiB;AACtD,MAAM,aAAa,IAAI,OAAO,UAAU;AACxC,MAAM,aAAa,IAAI,OAAO,UAAU;AAQjC,MAAM,eAAe,CAC1B,OACA,MAAe,OACS;AACxB,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAA;AAAA,EAChB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM;AAAA,IACJ,cAAc;AAAA,IACd,eAAe;AAAA,IACf,SAAS;AAAA,IACT,WAAW;AAAA,EAAA,IACT;AACJ,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IAAA;AAAA,IAEF;AAAA,EAAA;AAEF,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AACvB,aAAO;AAAA,IACT;AACA,WAAO,aAAa;AAAA,EACtB;AACA,MAAI,WAAW,KAAK,KAAK,GAAG;AAC1B,QAAI,WAAW,UAAU;AACvB,eAAS,UAAU,KAAK;AACxB,aAAO;AAAA,IACT;AACA,UAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,cAAQ,QAAA;AAAA,QACN,KAAK;AAAA,QACL,KAAK,YAAY;AACf,mBAAS,UAAU,aAAa;AAChC,iBAAO;AAAA,QACT;AAAA,QACA,SAAS;AACP,cAAI,UAAU;AACZ,qBAAS,UAAU,aAAa;AAChC,mBAAO;AAAA,UACT;AACA,gBAAMA,OAAM;AACZ,mBAAS,UAAUA,IAAG;AACtB,iBAAOA;AAAAA,QACT;AAAA,MAAA;AAAA,IAEJ,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI,IAAI,WAAW,QAAQ;AACzB,QAAI,SAAS;AAAA,EACf;AACA,UAAQ,MAAM,YAAA;AACd,MAAI,kBAAkB,KAAK,KAAK,KAAK,MAAM,SAAS,GAAG,GAAG;AACxD,UAAM,aAAa,MAAM,QAAQ,mBAAmB,EAAE,EAAE,QAAQ,OAAO,EAAE;AACzE,UAAM,CAAC,QAAQ,IAAI,OAAO,EAAE,IAAI,WAAW,YAAY;AAAA,MACrD,WAAW;AAAA,IAAA,CACZ;AACD,QAAI,SAAS,MAAM;AACjB,UAAI,WAAW,UAAU;AACvB,cAAM,aAAa,aAAa,OAAO,GAAG;AAC1C,cAAM,YAAY,aAAa,MAAM,GAAG;AACxC,YAAIA;AACJ,YAAI,cAAc,WAAW;AAC3BA,iBAAM,cAAc,UAAU,KAAK,SAAS;AAAA,QAC9C,OAAO;AACLA,iBAAM;AAAA,QACR;AACA,iBAAS,UAAUA,IAAG;AACtB,eAAOA;AAAAA,MACT;AACA,UAAI;AACJ,UAAI,gBAAgB,QAAQ;AAC1B,wBAAgB,aAAa,MAAM,GAAG;AAAA,MACxC,OAAO;AACL,wBAAgB,aAAa,OAAO,GAAG;AAAA,MACzC;AACA,UAAIA;AACJ,UAAI,yBAAyB,YAAY;AACvC,YAAI,UAAU;AACZA,iBAAM;AAAA,QACR,OAAO;AACLA,iBAAM;AAAA,QACR;AAAA,MACF,OAAO;AACLA,eAAM;AAAA,MACR;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAAA,IACT;AAEA,YAAQ,QAAA;AAAA,MACN,KAAK,UAAU;AACb,iBAAS,UAAU,EAAE;AACrB,eAAO;AAAA,MACT;AAAA,MACA,KAAK;AAAA,MACL,KAAK,YAAY;AACf,iBAAS,UAAU,IAAI;AACvB,eAAO,IAAI,WAAA;AAAA,MACb;AAAA,MACA,KAAK;AAAA,MACL,SAAS;AACP,cAAMA,OAAM;AACZ,iBAAS,UAAUA,IAAG;AACtB,eAAOA;AAAAA,MACT;AAAA,IAAA;AAAA,EAEJ;AACA,MAAI,WAAW,KAAK,KAAK,GAAG;AAC1B,UAAM,gBAAgB,qBAAqB,OAAO,GAAG;AACrD,QAAI,WAAW,UAAU;AACvB,UAAIA;AACJ,UAAI,yBAAyB,YAAY;AACvC,YAAI,UAAU;AACZA,iBAAM;AAAA,QACR,OAAO;AACLA,iBAAM;AAAA,QACR;AAAA,MACF,OAAO;AACLA,eAAM;AAAA,MACR;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAAA,IACT;AACA,QAAI,WAAW,UAAU;AACvB,UAAIA,OAAM;AACV,UAAI,yBAAyB,YAAY;AACvCA,eAAM;AAAA,MACR,OAAO;AACLA,eAAM;AAAA,MACR;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAAA,IACT;AACA,QAAI,yBAAyB,YAAY;AACvC,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,YAAQ,QAAQ,OAAO,GAAG;AAAA,EAC5B;AACA,MAAI,KAAK;AACT,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,QAAQ;AACZ,MAAI,UAAU,eAAe;AAC3B,YAAQ,QAAA;AAAA,MACN,KAAK,UAAU;AACb,iBAAS,UAAU,KAAK;AACxB,eAAO;AAAA,MACT;AAAA,MACA,KAAK,OAAO;AACV,iBAAS,UAAU,IAAI;AACvB,eAAO,IAAI,WAAA;AAAA,MACb;AAAA,MACA,KAAK,YAAY;AACf,cAAMA,OAAM;AACZ,iBAAS,UAAUA,IAAG;AACtB,eAAOA;AAAAA,MACT;AAAA,MACA,KAAK;AAAA,MACL,SAAS;AACP,cAAMA,OAAM;AACZ,iBAAS,UAAUA,IAAG;AACtB,eAAOA;AAAAA,MACT;AAAA,IAAA;AAAA,EAEJ,WAAW,UAAU,gBAAgB;AACnC,QAAI,WAAW,UAAU;AACvB,eAAS,UAAU,KAAK;AACxB,aAAO;AAAA,IACT;AACA,QAAI,cAAc;AAChB,UAAI;AACJ,UAAI,aAAa,WAAW,MAAM,GAAG;AACnC,wBAAgB,gBAAgB,cAAc,GAAG;AAAA,MACnD,WAAW,aAAa,WAAW,QAAQ,GAAG;AAC5C,wBAAgB,iBAAiB,cAAc,GAAG;AAAA,MACpD,OAAO;AACL,wBAAgB,kBAAkB,cAAc,GAAG;AAAA,MACrD;AACA,UAAI,yBAAyB,YAAY;AACvC,iBAAS,UAAU,aAAa;AAChC,eAAO;AAAA,MACT;AACA,OAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,IACzB,WAAW,WAAW,UAAU;AAC9B,YAAMA,OAAM;AACZ,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAAA,IACT;AAAA,EACF,WAAW,WAAW,UAAU;AAC9B,QAAI,MAAM,WAAW,MAAM,GAAG;AAC5B,YAAMA,OAAM,gBAAgB,OAAO,GAAG;AACtC,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAAA,IACT,WAAW,MAAM,WAAW,QAAQ,GAAG;AACrC,YAAM,CAAC,KAAK,IAAI,IAAI,IAAI,EAAE,IAAI;AAAA,QAC5B;AAAA,QACA;AAAA,MAAA;AAEF,UAAIA,OAAM;AACV,UAAI,OAAO,GAAG;AACZA,eAAM,SAAS,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,MACtC,OAAO;AACLA,eAAM,SAAS,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,MAC9C;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAAA,IACT,OAAO;AACL,YAAM,MAAM,kBAAkB,OAAO,GAAG;AACxC,UAAI,SAAS,GAAG,GAAG;AACjB,iBAAS,UAAU,GAAG;AACtB,eAAO;AAAA,MACT;AACA,YAAM,CAAC,KAAK,IAAI,IAAI,IAAI,EAAE,IAAI;AAC9B,UAAIA,OAAM;AACV,UAAI,QAAQ,OAAO;AACjB,YAAI,OAAO,GAAG;AACZA,iBAAM,GAAG,GAAG,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,QAClC,OAAO;AACLA,iBAAM,GAAG,GAAG,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,QAC1C;AAAA,MACF,WAAW,OAAO,GAAG;AACnBA,eAAM,GAAG,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,MAChC,OAAO;AACLA,eAAM,GAAG,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,MACxC;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAAA,IACT;AAAA,EACF,WAAW,MAAM,WAAW,MAAM,GAAG;AACnC,QAAI,eAAe,KAAK,KAAK,GAAG;AAC9B,UAAI,cAAc;AAChB,gBAAQ,MAAM,QAAQ,iBAAiB,YAAY;AAAA,MACrD;AAAA,IACF;AACA,QAAI,cAAc,KAAK,KAAK,GAAG;AAC7B,cAAQ,MAAM,QAAQ,gBAAgB,eAAe;AAAA,IACvD;AACA,UAAM,gBAAgB,gBAAgB,OAAO,GAAG;AAChD,QAAI,yBAAyB,YAAY;AACvC,eAAS,UAAU,aAAa;AAChC,aAAO;AAAA,IACT;AACA,KAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACzB,WAAW,MAAM,WAAW,QAAQ,GAAG;AACrC,UAAM,gBAAgB,iBAAiB,OAAO,GAAG;AACjD,QAAI,yBAAyB,YAAY;AACvC,eAAS,UAAU,aAAa;AAChC,aAAO;AAAA,IACT;AACA,KAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACzB,WAAW,OAAO;AAChB,UAAM,gBAAgB,kBAAkB,OAAO,GAAG;AAClD,QAAI,yBAAyB,YAAY;AACvC,eAAS,UAAU,aAAa;AAChC,aAAO;AAAA,IACT;AACA,KAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACzB;AACA,MAAI,MAAM;AACV,UAAQ,QAAA;AAAA,IACN,KAAK,OAAO;AACV,UACE,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,KAAK,KAClB,UAAU,GACV;AACA,iBAAS,UAAU,IAAI;AACvB,eAAO,IAAI,WAAA;AAAA,MACb;AACA,YAAM,gBAAgB,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAClC;AAAA,IACF;AAAA,IACA,KAAK,YAAY;AACf,UACE,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,KAAK,GAClB;AACA,iBAAS,UAAU,IAAI;AACvB,eAAO,IAAI,WAAA;AAAA,MACb;AACA,YAAM,gBAAgB,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC;AACtC;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL,SAAS;AACP,cAAQ,IAAA;AAAA,QACN,KAAK,OAAO;AACV,cAAI,UAAU,GAAG;AACf,kBAAM,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;AAAA,UAC9B,OAAO;AACL,kBAAM,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK;AAAA,UACzC;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,SAAS;AACZ,cAAI,UAAU,GAAG;AACf,kBAAM,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,UAC5B,OAAO;AACL,kBAAM,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK;AAAA,UACvC;AACA;AAAA,QACF;AAAA;AAAA,QAEA,SAAS;AACP,cAAI,UAAU,GAAG;AACf,kBAAM,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,UAClC,OAAO;AACL,kBAAM,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK;AAAA,UAC7C;AAAA,QACF;AAAA,MAAA;AAAA,IAEJ;AAAA,EAAA;AAEF,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AA4CO,MAAM,UAAU,CAAC,OAAe,MAAe,OAAsB;AAC1E,MAAI,WAAW;AACf,QAAM,gBAAgB,aAAa,OAAO,GAAG;AAC7C,MAAI,yBAAyB,YAAY;AACvC,WAAO;AAAA,EACT;AACA,SAAO;AACT;"}