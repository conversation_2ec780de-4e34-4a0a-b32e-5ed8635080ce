/**
 * Tests for game logic utility functions
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { 
  canAfford, 
  payCost, 
  formatCost, 
  toolLevel, 
  hasTool, 
  buildingLevel, 
  hasBuilding,
  isCampfireBuilt,
  isCampfireActive,
  log
} from '../../utils/gameLogic';
import type { GameState } from '../../types/game';
import { createInitialState } from '../../utils/persistence';

describe('Game Logic Utils', () => {
  let gameState: GameState;

  beforeEach(() => {
    gameState = createInitialState();
  });

  describe('Resource Management', () => {
    describe('canAfford', () => {
      it('should return true for null/undefined cost', () => {
        expect(canAfford(gameState, null)).toBe(true);
        expect(canAfford(gameState, undefined)).toBe(true);
      });

      it('should return true when player has enough resources', () => {
        gameState.resources.wood = 5;
        gameState.resources.herbs = 3;
        
        expect(canAfford(gameState, { wood: 2, herbs: 1 })).toBe(true);
        expect(canAfford(gameState, { wood: 5, herbs: 3 })).toBe(true);
      });

      it('should return false when player lacks resources', () => {
        gameState.resources.wood = 1;
        gameState.resources.herbs = 0;
        
        expect(canAfford(gameState, { wood: 2 })).toBe(false);
        expect(canAfford(gameState, { herbs: 1 })).toBe(false);
        expect(canAfford(gameState, { wood: 1, herbs: 1 })).toBe(false);
      });
    });

    describe('payCost', () => {
      it('should not modify resources for null/undefined cost', () => {
        const originalWood = gameState.resources.wood;
        payCost(gameState, null);
        expect(gameState.resources.wood).toBe(originalWood);
      });

      it('should deduct resources correctly', () => {
        gameState.resources.wood = 5;
        gameState.resources.herbs = 3;
        
        payCost(gameState, { wood: 2, herbs: 1 });
        
        expect(gameState.resources.wood).toBe(3);
        expect(gameState.resources.herbs).toBe(2);
      });

      it('should not go below zero', () => {
        gameState.resources.wood = 1;
        
        payCost(gameState, { wood: 5 });
        
        expect(gameState.resources.wood).toBe(0);
      });
    });

    describe('formatCost', () => {
      it('should return empty string for null/undefined cost', () => {
        expect(formatCost(null)).toBe('');
        expect(formatCost(undefined)).toBe('');
      });

      it('should format single resource cost', () => {
        expect(formatCost({ wood: 2 })).toBe('2 wood');
      });

      it('should format multiple resource costs', () => {
        const result = formatCost({ wood: 2, herbs: 1 });
        expect(result).toContain('2 wood');
        expect(result).toContain('1 herbs');
        expect(result).toContain(', ');
      });

      it('should ignore zero costs', () => {
        expect(formatCost({ wood: 2, herbs: 0 })).toBe('2 wood');
      });
    });
  });

  describe('Inventory Management', () => {
    describe('toolLevel and hasTool', () => {
      it('should return 0 for non-existent tools', () => {
        expect(toolLevel(gameState, 'spear')).toBe(0);
        expect(hasTool(gameState, 'spear')).toBe(false);
      });

      it('should return correct level for existing tools', () => {
        gameState.inventory.tools = { spear: 1 };
        
        expect(toolLevel(gameState, 'spear')).toBe(1);
        expect(hasTool(gameState, 'spear')).toBe(true);
      });

      it('should handle missing inventory structure', () => {
        gameState.inventory = { tools: {}, buildings: {} };
        expect(toolLevel(gameState, 'spear')).toBe(0);
        expect(hasTool(gameState, 'spear')).toBe(false);
      });
    });

    describe('buildingLevel and hasBuilding', () => {
      it('should return 0 for non-existent buildings', () => {
        expect(buildingLevel(gameState, 'campfire')).toBe(0);
        expect(hasBuilding(gameState, 'campfire')).toBe(false);
      });

      it('should return correct level for existing buildings', () => {
        gameState.inventory.buildings = { campfire: 1 };
        
        expect(buildingLevel(gameState, 'campfire')).toBe(1);
        expect(hasBuilding(gameState, 'campfire')).toBe(true);
      });
    });
  });

  describe('Campfire System', () => {
    describe('isCampfireBuilt', () => {
      it('should return false when campfire is not built', () => {
        expect(isCampfireBuilt(gameState)).toBe(false);
      });

      it('should return true when campfire is built', () => {
        gameState.inventory.buildings = { campfire: 1 };
        expect(isCampfireBuilt(gameState)).toBe(true);
      });
    });

    describe('isCampfireActive', () => {
      it('should return false when campfire is not built', () => {
        const now = Date.now();
        expect(isCampfireActive(gameState, now)).toBe(false);
      });

      it('should return false when campfire is built but expired', () => {
        gameState.inventory.buildings = { campfire: 1 };
        gameState.flags.fireExpiresAtMs = Date.now() - 1000; // expired
        
        expect(isCampfireActive(gameState, Date.now())).toBe(false);
      });

      it('should return true when campfire is built and active', () => {
        gameState.inventory.buildings = { campfire: 1 };
        gameState.flags.fireExpiresAtMs = Date.now() + 10000; // active for 10 more seconds
        
        expect(isCampfireActive(gameState, Date.now())).toBe(true);
      });
    });
  });

  describe('Chronicle System', () => {
    describe('log', () => {
      it('should add entries to the beginning of chronicle', () => {
        const initialLength = gameState.chronicle.length;
        
        log(gameState, 'Test message');
        
        expect(gameState.chronicle.length).toBe(initialLength + 1);
        expect(gameState.chronicle[0].text).toBe('Test message');
        expect(gameState.chronicle[0].day).toBe(gameState.flags.day);
      });

      it('should preserve existing chronicle entries', () => {
        const originalFirst = gameState.chronicle[0];
        
        log(gameState, 'New message');
        
        expect(gameState.chronicle[1]).toEqual(originalFirst);
      });
    });
  });
});
