{"name": "mdn-data", "version": "2.12.2", "description": "Open Web data by the Mozilla Developer Network", "main": "index.js", "files": ["api/index.js", "api/*.json", "css/index.js", "css/*.json", "l10n/index.js", "l10n/*.json"], "repository": {"type": "git", "url": "https://github.com/mdn/data.git"}, "keywords": ["data", "mdn", "mozilla", "css"], "author": "Mozilla Developer Network", "license": "CC0-1.0", "bugs": {"url": "https://github.com/mdn/data/issues"}, "homepage": "https://developer.mozilla.org", "devDependencies": {"ajv": "^6.12.6", "better-ajv-errors": "^1.1.2"}, "scripts": {"lint": "node test/lint", "test": "npm run lint"}}