# Dice & Dread: Wards of Hexwood

A minimalist daily-dice survival game with fantasy theme. Manage resources, craft tools, build shelter, and survive in the mysterious Hexwood using dice-based actions.

## 🎮 Game Overview

**Dice & Dread** is a turn-based survival game where each day you roll dice and assign them to various actions. Your goal is to survive as long as possible by managing your health, hunger, thirst, morale, defense, and temperature while exploring the dangerous Hexwood.

### Core Mechanics

- **Daily Dice System**: Roll dice each day and assign them to actions
- **Resource Management**: Gather food, water, wood, herbs, ore, and magical shards
- **Survival Stats**: Maintain health, hunger, thirst, morale, defense, and temperature
- **Crafting & Building**: Create tools and structures to improve survival chances
- **Random Events**: Day and night events that can help or hinder your progress
- **Real-time Campfire**: Fire burns in real-time and provides warmth
- **Chronicle System**: Track your journey through detailed event logs

## 🎯 Victory Conditions

There is no traditional "win" condition - the goal is to survive as many days as possible. Death occurs when your health reaches 0.

## 📊 Game Systems

### Stats (0-100 scale)
- **Health**: Your life force - reaching 0 means death
- **Hunger**: Decreases without food, affects health when too low
- **Thirst**: Decreases without water, affects health when too low
- **Morale**: Affects various outcomes and events
- **Defense**: Reduces damage from events and failed actions
- **Temperature**: Comfort level - extreme temperatures cause damage

### Resources
- **Food**: Consumed daily (2 per day), prevents hunger
- **Water**: Consumed daily (2 per day), prevents thirst
- **Wood**: Used for crafting, building, and maintaining campfire
- **Herbs**: Used for healing items and trading
- **Ore**: Rare material for advanced crafting
- **Shards**: Magical crystals with special properties

### Tools
- **Spear**: Improves hunting and foraging, provides +5 defense
- **Waterskin**: Increases water gathering efficiency
- **Cloak**: Provides temperature regulation (+3 heat buff)

### Buildings
- **Campfire**: Provides warmth, burns in real-time, requires wood to maintain
- **Lean-to**: Shelter that provides +3 defense and +2 morale
- **Ward Sigils**: Magical protection that decays over time

## 🎲 Actions System

### Gathering Actions
- **Forage Food**: Base 35% success, improved by spear
- **Gather Water**: Base 45% success, improved by waterskin
- **Scavenge Wood**: Base 40% success, essential for fire and crafting
- **Gather Herbs**: Base 30% success, used for healing and trading
- **Hunt**: Base 30% success, high risk/reward, improved by spear
- **Scout**: Base 35% success, increases event chances and provides bonuses
- **Study Lore**: Base 55% success, increases knowledge and max dice

### Crafting Actions
- **Craft Bandage**: Base 55% success, costs 1 herb + 1 wood, heals 6-12 health
- **Craft Spear**: Base 60% success, costs 2 wood, one-time craft, +5 defense
- **Build Campfire**: Base 70% success, costs 2 wood, one-time build
- **Build Lean-to**: Base 65% success, costs 3 wood, one-time build
- **Ward Camp**: Base 45% success, no cost, temporary magical protection

### Success Calculation
Success chance = Base Chance + (Die Value × 10) - Difficulty
- Minimum success chance: 5%
- Maximum success chance: 95%
- Higher die values significantly improve success rates

## 🌡️ Temperature System

Temperature affects survival and comfort:
- **Optimal Range**: Around 50 (comfortable)
- **Cold Effects**: Below 30 causes morale loss and potential health damage
- **Heat Effects**: Above 70 causes morale loss and potential health damage
- **Heat Sources**: Active campfire (+6), cloak (+3)
- **Cooling Sources**: Lean-to provides better airflow (+2 cold buff in heat)

## 🔥 Campfire Mechanics

The campfire is a unique real-time system:
- **Building**: Costs 2 wood, burns for 5 minutes initially
- **Stoking**: Add 1 wood = +5 minutes burn time (no die required)
- **Benefits**: Provides heat buff while burning
- **Real-time**: Burns continuously, even when game is closed
- **Visual Indicator**: Shows remaining burn time in sidebar

## 🌙 Day/Night Cycle

Each day consists of:
1. **Roll Dice**: Get dice equal to your max dice (starts at 4)
2. **Assign Actions**: Assign each die to an action
3. **Resolve Day**: All actions execute, consume daily resources
4. **Night Events**: Random events occur
5. **Rest & Recovery**: Heal based on ward level, advance to next day

### Daily Resource Consumption
- **Food**: 2 units consumed automatically
- **Water**: 2 units consumed automatically
- **Insufficient Resources**: Causes hunger/thirst loss and potential health damage

## 🎰 Random Events

### Day Events (20% base chance, increased by scouting)
- **Sudden Squall**: Provides water, cools ambient temperature
- **Sprite Trickery**: Random morale loss or herb gain
- **Gleaming Shard**: Find magical shards
- **Merchant's Cache**: Discover hidden food supplies
- **Warming Breeze**: Increases ambient temperature and morale
- **Ancient Rune Stone**: Gain lore if knowledgeable enough

### Night Events (always occur)
- **Whispers in the Bracken**: Morale damage, reduced by wards
- **Wandering Peddler**: Trade herbs for food and water
- **Shard Resonance**: Find shards, boosted by scouting
- **Restful Sleep**: Bonus healing and morale
- **Prowling Beast**: Potential damage, reduced by defense/wards
- **Starlit Meditation**: Gain lore if morale is high
- **Midnight Foraging**: Chance to find herbs

## 💾 Save System

- **Auto-save**: Game automatically saves after each day resolution
- **Local Storage**: Save data stored in browser's local storage
- **State Migration**: Handles backward compatibility for game updates
- **New Run**: Reset button creates fresh game state

## 🏗️ Technical Architecture

### Project Structure
```
src/
├── components/          # React components
│   ├── game/           # Game-specific UI components
│   └── ui/             # Reusable UI components (shadcn/ui)
├── constants/          # Game configuration and defaults
├── data/              # Game data (actions, events)
├── store/             # State management (reducer)
├── types/             # TypeScript type definitions
├── utils/             # Utility functions
│   ├── math.ts        # Mathematical utilities
│   ├── gameLogic.ts   # Game logic utilities
│   └── persistence.ts # Save/load functionality
└── test/              # Test files
```

### Key Technologies
- **React 19**: UI framework with hooks and modern patterns
- **TypeScript**: Type safety and better development experience
- **Vite**: Fast build tool and development server
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: High-quality React components
- **Framer Motion**: Smooth animations and transitions
- **Vitest**: Fast unit testing framework
- **Testing Library**: React component testing utilities

### State Management
- **Reducer Pattern**: Centralized state management with useReducer
- **Immutable Updates**: Pure functions for state transitions
- **Action-based**: All state changes through dispatched actions
- **Persistence**: Automatic save/load with state normalization

## 🚀 Development

### Prerequisites
- Node.js 18+
- npm or yarn package manager

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd wards-of-hexwood

# Install dependencies
npm install

# Start development server
npm run dev
```

### Available Scripts
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint for code quality
- `npm run test` - Run tests in watch mode
- `npm run test:run` - Run tests once
- `npm run test:ui` - Run tests with UI interface
- `npm run coverage` - Generate test coverage report

### Testing
The project includes comprehensive tests covering:
- **Unit Tests**: Game logic, utilities, and mathematical functions
- **Integration Tests**: State management and reducer logic
- **Component Tests**: React component behavior (planned)

Run tests with:
```bash
npm run test:run  # Run all tests once
npm run test      # Run tests in watch mode
```

### Code Quality
- **TypeScript**: Strict type checking enabled
- **ESLint**: Code linting with React and TypeScript rules
- **Prettier**: Code formatting (recommended to set up in your editor)

## 🎨 Customization & Extension

### Adding New Actions
1. Define action in `src/data/actions.ts`
2. Add action key to `ActionKey` type in `src/types/game.ts`
3. Update default assignments in `src/constants/game.ts`

### Adding New Events
1. Add event to `DAY_EVENTS` or `NIGHT_EVENTS` in `src/data/events.ts`
2. Define event logic with weight and run function

### Modifying Game Balance
- Adjust constants in `src/constants/game.ts`
- Modify action success rates and costs in `src/data/actions.ts`
- Update resource consumption rates in game reducer

### UI Customization
- Modify Tailwind classes in components
- Update shadcn/ui theme in `src/index.css`
- Add new components in `src/components/game/`

## 📈 Future Development Ideas

### Gameplay Enhancements
- **Character Classes**: Different starting stats and abilities
- **Skill Trees**: Unlock new actions and bonuses
- **Seasons**: Changing weather patterns and resource availability
- **Locations**: Multiple areas to explore with unique resources
- **Companions**: NPCs that can help with actions
- **Magic System**: Expanded use of shards for magical effects

### Technical Improvements
- **Multiplayer**: Shared world or competitive survival
- **Mobile Support**: Touch-friendly interface and PWA features
- **Sound Design**: Ambient sounds and effect audio
- **Animations**: Enhanced visual feedback for actions
- **Analytics**: Track player behavior and game balance
- **Accessibility**: Screen reader support and keyboard navigation

### Quality of Life
- **Action Queue**: Plan multiple days in advance
- **Statistics**: Detailed survival statistics and achievements
- **Export/Import**: Share save files between devices
- **Difficulty Settings**: Adjustable challenge levels
- **Tutorial**: Interactive guide for new players

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request. For major changes, please open an issue first to discuss what you would like to change.

### Development Guidelines
1. Follow existing code style and patterns
2. Add tests for new functionality
3. Update documentation as needed
4. Ensure all tests pass before submitting PR

---

*Survive the Hexwood, one die at a time.* 🎲🌲
